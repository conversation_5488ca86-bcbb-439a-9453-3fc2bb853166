#!/bin/bash
#
# Zoe - The Odoo Technical Auditor - Definitive Validation Script v1.1
# SUPERIOR VERSION: Dynamically checks module status and uses the correct
# installation (-i) or upgrade (-u) flag for a reliable test.
#

# --- ODOO ENVIRONMENT CONFIGURATION ---
ODOO_USER="vetlane"
ODOO_PYTHON_PATH="/vetlane/venv/bin/python"
ODOO_BIN_PATH="/vetlane/odoo18/odoo-bin"
ODOO_CONF_PATH="/etc/odoo18_vetlane.conf"
ODOO_LOG_FILE="/var/log/odoo/odoo18_vetlane.log"
ODOO_DB_NAME="vetlane_test"
# --- END CONFIGURATION ---

# Check if a module name was provided
if [ -z "$1" ]; then
    echo "? ERROR: No module name provided."
    echo "Usage: ./validate.sh <module_name>"
    exit 1
fi

MODULE_NAME=$1

echo "================================================="
echo "Starting Live Validation (v1.1) for Module: $MODULE_NAME"
echo "================================================="

# 1. Determine the module's current status in the database
echo "--> Checking current status of module '$MODULE_NAME' in database '$ODOO_DB_NAME'..."
MODULE_STATUS=$(sudo -u $ODOO_USER $ODOO_PYTHON_PATH $ODOO_BIN_PATH shell -d $ODOO_DB_NAME --no-http --logfile /dev/null <<EOF
import odoo
from odoo import SUPERUSER_ID
with odoo.registry().cursor() as cr:
    env = odoo.api.Environment(cr, SUPERUSER_ID, {})
    module = env['ir.module.module'].search([('name', '=', '$MODULE_NAME')], limit=1)
    print(module.state if module else 'uninstalled')
EOF
)
# Get the last line of output which contains the status
MODULE_STATUS=$(echo "$MODULE_STATUS" | tail -n 1)
echo "    Current Status: $MODULE_STATUS"

# 2. Choose the correct flag based on the status
if [[ "$MODULE_STATUS" == "uninstalled" ]]; then
    INSTALL_FLAG="-i"
    ACTION="installation"
else
    INSTALL_FLAG="-u"
    ACTION="upgrade"
fi
echo "    Action Required: Fresh $ACTION. Using flag '$INSTALL_FLAG'."

# 3. Add a unique marker to the log file
LOG_MARKER="--- VALIDATION START FOR $MODULE_NAME @ $(date) ---"
echo "$LOG_MARKER" | sudo -u $ODOO_USER tee -a $ODOO_LOG_FILE > /dev/null

# 4. Construct and execute the command
INSTALL_COMMAND="sudo -u $ODOO_USER $ODOO_PYTHON_PATH $ODOO_BIN_PATH -c $ODOO_CONF_PATH -d $ODOO_DB_NAME $INSTALL_FLAG $MODULE_NAME --stop-after-init"

echo "Executing $ACTION command..."
echo "$INSTALL_COMMAND"
echo "-------------------------------------------------"
eval "$INSTALL_COMMAND" || true
echo "-------------------------------------------------"
echo "Command finished. Analyzing log file for results..."

# 5. Check the log file for success or failure
LOG_RESULT=$(awk -v marker="$LOG_MARKER" 'BEGIN{p=0} $0~marker{p=1;next} p' $ODOO_LOG_FILE)
SUCCESS_MESSAGE="odoo.modules.registry: Registry loaded"

if echo "$LOG_RESULT" | grep -q "$SUCCESS_MESSAGE"; then
    echo "? VALIDATION SUCCESS: Odoo registry loaded successfully after module $ACTION."
    echo "The module '$MODULE_NAME' has been tested correctly in the database."
    echo "================================================="
    exit 0
else
    echo "? VALIDATION FAILED: An error occurred during the $ACTION."
    echo "--- Relevant Log Snippet ---"
    if echo "$LOG_RESULT" | grep -q "ERROR"; then
        echo "$LOG_RESULT" | grep "ERROR" -C 10
    else
        echo "No specific 'ERROR' line found. Showing last 20 lines of log since marker:"
        echo "$LOG_RESULT" | tail -20
    fi
    echo "----------------------------"
    echo "================================================="
    exit 1
fi