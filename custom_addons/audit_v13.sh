#!/bin/bash
#
# Zoe - The Odoo Technical Auditor - Definitive Audit Script v13.0
# FINAL VERSION: Implements the complete multi-layered workflow with DETAILED reporting for all checks.
#

# --- CONFIGURATION ---
MODULE_NAME="[SET_MODULE_NAME_HERE]"
BASE_ADDONS_PATH="/vetlane/custom_addons/vetlane_custom_modules"
ODOO_USER="vetlane"
ODOO_PYTHON_PATH="/vetlane/venv/bin/python"
ODOO_BIN_PATH="/vetlane/odoo18/odoo-bin"
ODOO_CONF_PATH="/etc/odoo18_vetlane.conf"
ODOO_DB_NAME="vetlane_test"
PYLINT_PATH="/vetlane/venv/bin/pylint"
PYLINTRC_PATH="/vetlane/.pylintrc"
# --- END CONFIGURATION ---

set -o pipefail

# --- INITIALIZATION ---
MODULE_PATH="$BASE_ADDONS_PATH/$MODULE_NAME"
REPORTS_DIR="$(pwd)/audit_reports_${MODULE_NAME}_$(date +%Y%m%d_%H%M%S)"

echo "================================================="
echo "Starting Definitive Audit (v12.0) for Module: $MODULE_NAME"
echo "================================================="

if [ ! -d "$MODULE_PATH" ]; then
    echo "? ERROR: Module directory not found at '$MODULE_PATH'."
    exit 1
fi
mkdir -p "$REPORTS_DIR"
echo "? Reports will be saved in: $REPORTS_DIR"
echo ""

cd "$MODULE_PATH"

# --- AUDIT EXECUTION (CORRECT FUNNEL ORDER WITH DETAILED REPORTING) ---

echo ">>> (1/7) Performing Odoo-Aware Static Analysis (The First Filter)..."
if [ -f "$PYLINT_PATH" ] && [ -f "$PYLINTRC_PATH" ]; then
    "$PYLINT_PATH" --rcfile="$PYLINTRC_PATH" --load-plugins=pylint_odoo . > "$REPORTS_DIR/1_pylint_report.txt" || true
    echo "    Done. See '1_pylint_report.txt'"
else
    echo "    ?? WARNING: pylint or .pylintrc not found. Skipping static analysis."
    echo "pylint check skipped." > "$REPORTS_DIR/1_pylint_report.txt"
fi

echo ">>> (2/7) Performing Intelligent Live Installation Test..."
{
    echo "### Odoo Module Installation/Upgrade Test ###"
    MODULE_STATUS=$(sudo -u $ODOO_USER $ODOO_PYTHON_PATH $ODOO_BIN_PATH shell -d $ODOO_DB_NAME --no-http --logfile /dev/null <<EOF
import odoo
from odoo import SUPERUSER_ID
with odoo.registry('$ODOO_DB_NAME').cursor() as cr:
    env = odoo.api.Environment(cr, SUPERUSER_ID, {})
    module = env['ir.module.module'].search([('name', '=', '$MODULE_NAME')], limit=1)
    print(module.state if module else 'uninstalled')
EOF
)
    MODULE_STATUS=$(echo "$MODULE_STATUS" | tail -n 1)
    echo "    Current Status: $MODULE_STATUS"

    if [[ "$MODULE_STATUS" == "uninstalled" ]]; then
        INSTALL_FLAG="-i"
        ACTION="installation"
    else
        INSTALL_FLAG="-u"
        ACTION="upgrade"
    fi
    echo "    Action to perform: $ACTION (using flag $INSTALL_FLAG)"

    INSTALL_COMMAND="sudo -u $ODOO_USER $ODOO_PYTHON_PATH $ODOO_BIN_PATH -c $ODOO_CONF_PATH -d $ODOO_DB_NAME $INSTALL_FLAG $MODULE_NAME --stop-after-init"
    echo "    Executing command: $INSTALL_COMMAND"
    echo "--------------------------------------------------------------------"
    eval "$INSTALL_COMMAND" || true
    echo "--------------------------------------------------------------------"
} > "$REPORTS_DIR/2_installation_test_log.txt" 2>&1
echo "    Done. See '2_installation_test_log.txt' for the result."

echo ">>> (3/7) Performing Comprehensive Model & Class Discovery..."
{
    awk '
    /class .*\(models\.(Model|TransientModel|AbstractModel)\)/ { match($0, /class ([A-Za-z0-9_]+)/, arr); print "py_class:" arr[1]; }
    /_name[[:space:]]*=/ { match($0, /_name[[:space:]]*=[[:space:]]*["'\'']([^"'"'"']+)/, arr); print "py_model_name:" arr[1]; }
    /_inherit[[:space:]]*=/ { match($0, /_inherit[[:space:]]*=[[:space:]]*["'\'']([^"'"'"']+)/, arr); print "py_model_inherit:" arr[1]; }
    ' $(find . -name "*.py") | sort | uniq
} > "$REPORTS_DIR/3_discovery_report.txt"
echo "    Done."

echo ">>> (4/7) Performing Model Cross-Reference Analysis..."
{
    grep "py_model_name:" "$REPORTS_DIR/3_discovery_report.txt" | cut -d':' -f2 | sort | uniq > "$REPORTS_DIR/temp_py_models_clean.txt"
    grep -hRo '<record [^>]*model="[^"]*"' . --include='*.xml' | sed -e 's/.*model="\([^"]*\)".*/\1/' | sort | uniq > "$REPORTS_DIR/temp_xml_models_clean.txt"

    echo "### Models in XML/Data Files but NOT Defined with _name in Python ###"
    comm -23 "$REPORTS_DIR/temp_xml_models_clean.txt" "$REPORTS_DIR/temp_py_models_clean.txt"
} > "$REPORTS_DIR/4_model_cross_reference_report.txt"
echo "    Done."

echo ">>> (5/7) Performing Detailed Field Mismatch Analysis..."
{
    grep -hRoP '^\s*\K[a-zA-Z0-9_]+(?=\s*=\s*fields\.)' . --include='*.py' | sort | uniq > "$REPORTS_DIR/temp_model_fields.txt"
    awk '
    BEGIN { OFS="," }
    /<record.*model="ir.ui.view"/ { in_view = 1 }
    in_view && /<field.*name="model"/ { match($0, />([^<]+)</, arr); model = arr[1] }
    /<field.*name=/ { if (in_view) { match($0, /name="([^"]+)"/, f); print FILENAME, FNR, model, f[1] } }
    /<\/record>/ { in_view = 0; model = "unknown" }
    ' $(find . -name "*.xml") | sort -u > "$REPORTS_DIR/temp_view_fields_with_context.txt"

    echo "### Detailed Field Mismatch Report (Contextual) ###"
    echo "FORMAT: XML_FILE,LINE_NUMBER,VIEW_MODEL,FIELD_NAME"
    echo "-------------------------------------------------"
    while IFS=, read -r file line model field; do
        if ! grep -q -w "$field" "$REPORTS_DIR/temp_model_fields.txt"; then
            echo "$file,$line,$model,$field"
        fi
    done < "$REPORTS_DIR/temp_view_fields_with_context.txt"
} > "$REPORTS_DIR/5_field_mismatch_report_detailed.txt"
echo "    Done."

echo ">>> (6/7) Performing Security Rule Analysis..."
{
    grep "py_model_name:" "$REPORTS_DIR/3_discovery_report.txt" | cut -d':' -f2 | sort | uniq > "$REPORTS_DIR/temp_py_models_clean.txt"
    if [ -f "security/ir.model.access.csv" ]; then
        tail -n +2 "security/ir.model.access.csv" | cut -d',' -f3 | sed 's/model_//' | sed 's/_/./g' | sort | uniq > "$REPORTS_DIR/temp_secured_models.txt"
        echo "### Models MISSING from ir.model.access.csv ###"
        comm -23 "$REPORTS_DIR/temp_py_models_clean.txt" "$REPORTS_DIR/temp_secured_models.txt"
    else
        echo "### CRITICAL: security/ir.model.access.csv not found! ###"
        cat "$REPORTS_DIR/temp_py_models_clean.txt"
    fi
} > "$REPORTS_DIR/6_security_analysis_report.txt"
echo "    Done."

echo ">>> (7/7) Generating File Inventory..."
find . -type f | sort > "$REPORTS_DIR/7_file_inventory.txt"
echo "    Done."

# Clean up all temp files
rm -f "$REPORTS_DIR"/temp_*.txt

echo ""
echo "================================================="
echo "? Definitive Audit (v13.0) finished successfully."
echo "Reports are located in: $REPORTS_DIR"
echo "================================================="