<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- product.product -->
        <record id="hms_registration_service_0" model="product.product">
            <field name="name">Registration Service</field>
            <field name="type">service</field>
            <field name="hospital_product_type">consultation</field>
            <field name="list_price">100.0</field>
            <field name="standard_price">100.0</field>
        </record>

        <record id="hms_consultation_service_0" model="product.product">
            <field name="name">Consultation Service</field>
            <field name="type">service</field>
            <field name="hospital_product_type">consultation</field>
            <field name="list_price">800.0</field>
            <field name="standard_price">800.0</field>
        </record>

        <record id="hms_followup_service_0" model="product.product">
            <field name="name">Follow-up Service</field>
            <field name="type">service</field>
            <field name="hospital_product_type">consultation</field>
            <field name="list_price">400.0</field>
            <field name="standard_price">400.0</field>
        </record>

        <!-- Company Data -->
        <record id="base.main_company" model="res.company" >
            <field name="patient_registration_product_id" ref="hms_registration_service_0"/>
            <field name="treatment_registration_product_id" ref="hms_registration_service_0"/>
            <field name="consultation_product_id" ref="hms_consultation_service_0"/>
            <field name="followup_product_id" ref="hms_followup_service_0"/>
            <field name="appointment_usage_location_id" ref="stock.stock_location_customers"/>
            <field name="appointment_stock_location_id" ref="stock.stock_location_stock"/>
        </record>

        <!-- <Patient> -->

        <record id="acs_patient_weight_uom" model="ir.config_parameter">
            <field name="key">acs_hms.acs_patient_weight_uom</field>
            <field name="value">Kg</field>
        </record>

        <record id="acs_patient_height_uom" model="ir.config_parameter">
            <field name="key">acs_hms.acs_patient_height_uom</field>
            <field name="value">Cm</field>
        </record>

        <record id="acs_patient_temp_uom" model="ir.config_parameter">
            <field name="key">acs_hms.acs_patient_temp_uom</field>
            <field name="value">°C</field>
        </record>

        <record id="acs_patient_spo2_uom" model="ir.config_parameter">
            <field name="key">acs_hms.acs_patient_spo2_uom</field>
            <field name="value">%</field>
        </record>

        <record id="acs_patient_rbs_uom" model="ir.config_parameter">
            <field name="key">acs_hms.acs_patient_rbs_uom</field>
            <field name="value">mg/dl</field>
        </record>


    </data>

    <data>
        <record forcecreate="True" id="cron_cancel_draft_appointments" model="ir.cron">
            <field name="name">Cancel old appointments</field>
            <field eval="True" name="active"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="state">code</field>
            <field ref="acs_hms.model_hms_appointment" name="model_id"/>
            <field eval="'model.cancel_old_appointments()'" name="code"/>
        </record>

        <record id="ir_cron_send_reminder_action" model="ir.cron">
            <field name="name">Send Appointment reminder</field>
            <field eval="True" name="active"/>
            <field name="interval_number">15</field>
            <field name="interval_type">minutes</field>
            <field name="state">code</field>
            <field ref="acs_hms.model_hms_appointment" name="model_id"/>
            <field eval="'model.send_appointment_reminder()'" name="code"/>
        </record>

        <record id="default_auto_cancel_old_appointment" model="ir.config_parameter" forcecreate="0">
            <field name="key">acs_hms.cancel_old_appointment</field>
            <field name="value">True</field>
        </record>

    </data>
</odoo>