
# **The Definitive Odoo 18 Remediation Playbook (v3.0)**

## **1. How to Use This Playbook**

This document is a comprehensive library of proven, systematic fix patterns for upgrading legacy Odoo modules to Odoo 18 compatibility. It is designed to be used as a core component of the workflow defined in **`System_Mandate_v12.0`**.

1.  **Identify the Blocker:** After the `audit.sh` script runs, the Initial Analysis will identify the highest-priority error (e.g., an installation failure traceback or a critical `pylint` error).
2.  **Find the Pattern:** Locate the specific error in this playbook to understand the root cause and the correct, modern solution.
3.  **Apply the Fix:** Use the "After" code example and "Application Rules" to create a precise `diff` patch that surgically fixes the issue.
4.  **Repeat:** Re-run the validation test.  If it fails with a new error, find the new error in this playbook and repeat the process.

-----

## **2. Python Code Remediation Patterns**

#### **Pattern 2.1: Correcting `openerp` Imports**

  * **Problem:** The module uses outdated `openerp` library imports, which were removed after Odoo 8.0.
  * **Before (Legacy):**
    ```python
    from openerp import models, fields, api
    import openerp.addons.decimal_precision as dp
    ```
  * **After (Odoo 18):**
    ```python
    from odoo import models, fields, api
    from odoo.addons import decimal_precision as dp
    ```

#### **Pattern 2.2: Removing Old API Decorators (`@api.one`, `@api.multi`)**

  * **Problem:** The code uses `@api.one` and `@api.multi`, which are deprecated. Odoo 18 methods operate on recordsets by default.
  * **Before (Legacy):**
    ```python
    @api.one
    def do_something(self):
        self.state = 'done'
    ```
  * **After (Odoo 18):**
    ```python
    def do_something(self):
        for record in self:
            record.state = 'done'
    ```

#### **Pattern 2.3: Modernizing OSV Patterns and `_columns`**

  * **Problem:** The module is defined using the legacy `osv.osv` class and `_columns` dictionary.
  * **Before (Legacy):**
    ```python
    from openerp.osv import osv
    class MyModel(osv.osv):
        _name = 'my.model'
        _columns = {
            'name': fields.char('Name', size=64),
        }
    ```
  * **After (Odoo 18):**
    ```python
    from odoo import models, fields
    class MyModel(models.Model):
        _name = 'my.model'
        name = fields.Char(string='Name')
    ```

#### **Pattern 2.4: Modernizing Exception Handling**

  * **Problem:** The code uses the legacy `Warning` exception.
  * **Before (Legacy):** `raise Warning(_('This is an old warning.'))`
  * **After (Odoo 18):** `raise UserError(_('This is a modern user error.'))`

#### **Pattern 2.5: Updating Deprecated Model Names**

  * **Problem:** The code inherits from a model whose name has changed in Odoo 18 (e.g., `stock.production.lot`).
  * **Before (Legacy):** `_inherit = 'stock.production.lot'`
  * **After (Odoo 18):** `_inherit = 'product.lot'`

#### **Pattern 2.6: Replacing Deprecated `_defaults`**

  * **Problem:** The code uses the `_defaults` dictionary to define default values.
  * **Before (Legacy):**
    ```python
    _defaults = {
        'is_active': True,
    }
    ```
  * **After (Odoo 18):**
    ```python
    is_active = fields.Boolean(string="Active", default=True)
    ```

-----

## **3. XML View Remediation Patterns**

#### **Pattern 3.1: Adding Missing `type` Fields in Views**

  * **Problem:** `ir.ui.view` records now require an explicit `type` field.
  * **Solution:** Add the appropriate `<field name="type">` tag based on the view type (`form`, `list`, `search`, etc.).

#### **Pattern 3.2: Converting `<tree>` to `<list>`**

  * **Problem:** The `<tree>` tag is deprecated for view definitions.
  * **Solution:** Convert all `<tree>` tags in `ir.ui.view` records to `<list>` tags, preserving all attributes.

#### **Pattern 3.3: Converting `states` and `attrs` Attributes**

  * **Problem:** The `states` and `attrs` attributes are deprecated for controlling visibility and readonly status.
  * **Before (Legacy):**
    ```xml
    <button name="action_confirm" states="draft,sent"/>
    <field name="description" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
    ```
  * **After (Odoo 18):**
    ```xml
    <button name="action_confirm" invisible="state not in ['draft', 'sent']"/>
    <field name="description" readonly="state != 'draft'"/>
    ```

#### **Pattern 3.4: Modernizing Kanban Templates**

  * **Problem:** Kanban views use deprecated `kanban-box` templates and old CSS classes.
  * **Before (Legacy):**
    ```xml
    <t t-name="kanban-box">
        <div class="oe_kanban_global_click oe_highlight">...</div>
    </t>
    ```
  * **After (Odoo 18):**
    ```xml
    <t t-name="card">
        <div class="o_kanban_record btn-primary">...</div>
    </t>
    ```

#### **Pattern 3.5: Updating Tree View Decorations**

  * **Problem:** The `colors` attribute in tree views is deprecated.
  * **Before (Legacy):** `<tree colors="red:state=='cancel'">`
  * **After (Odoo 18):** `<tree decoration-danger="state == 'cancel'">`

-----

## **4. Frontend & Manifest Remediation Patterns**

#### **Pattern 4.1: Modernizing JavaScript Module Definition**

  * **Problem:** JavaScript files use the old `odoo.define` system and `Widget.extend()`.
  * **Before (Legacy):**
    ```javascript
    odoo.define('my_module.my_widget', function (require) {
        var Widget = require('web.Widget');
        return Widget.extend({...});
    });
    ```
  * **After (Odoo 18 / OWL):**
    ```javascript
    /** @odoo-module **/
    import { Component } from "@odoo/owl";
    import { registry } from "@web/core/registry";
    class MyComponent extends Component {...}
    registry.category("actions").add("my_action", MyComponent);
    ```

#### **Pattern 4.2: Modernizing Manifest Asset Bundles**

  * **Problem:** The `__manifest__.py` file defines assets using deprecated `css`, `js`, and `qweb` keys.
  * **Before (Legacy):** `'qweb': ['static/src/xml/my_template.xml']`
  * **After (Odoo 18):**
    ```python
    'assets': {
        'web.assets_backend': [
            'my_module/static/src/xml/my_template.xml',
        ],
    }
    ```

-----

## **5. Other Common Remediation Patterns**

#### **Pattern 5.1: Replacing Deprecated Workflow System**

  * **Problem:** The module uses legacy `workflow.xml` files. The entire workflow engine was removed in Odoo 11.0.
  * **Solution:** This requires a complete refactor. The business logic must be re-implemented using `state` fields on the Python models, with methods that handle the state transitions.

#### **Pattern 5.2: Updating Report Template Structure**

  * **Problem:** Report templates call deprecated parent layouts.
  * **Before (Legacy):** `<t t-call="report.external_layout">`
  * **After (Odoo 18):** `<t t-call="web.external_layout">`

-----

## **6. Search Patterns for Bulk Audit**

Use these `grep` commands to perform a quick search for potential issues across the codebase.

#### **Python Patterns:**

  * `grep -r "from openerp import" . --include="*.py"`
  * `grep -r "@api.one\|@api.multi" . --include="*.py"`
  * `grep -r "osv.osv\|osv.Model" . --include="*.py"`
  * `grep -r "_columns\s*=" . --include="*.py"`
  * `grep -r "self.pool\[" . --include="*.py"`
  * `grep -r "signal_workflow" . --include="*.py"`

#### **XML Patterns:**

  * `grep -r 'states="' . --include="*.xml"`
  * `grep -r 'attrs="' . --include="*.xml"`
  * `grep -r "kanban-box" . --include="*.xml"`
  * `grep -r "oe_highlight\|oe_form_configuration" . --include="*.xml"`
  * `grep -r 'colors="' . --include="*.xml"`

#### **JavaScript Patterns:**

  * `grep -r "odoo.define" . --include="*.js"`
  * `grep -r "Widget.extend" . --include="*.js"`
  * `grep -r "this._rpc" . --include="*.js"`

-----

## **7. Troubleshooting the Audit Process**

#### **Issue 7.1: `pylint` Fails with Configuration Errors**

  * **Symptom:** The `1_pylint_report.txt` is full of `E0015: Unrecognized option found` or `E0401: Unable to import 'odoo'`.
  * **Root Cause:** The `/vetlane/.pylintrc` file is misconfigured, or the linter is not running inside the correct Python virtual environment.
  * **Solution:** Ensure the `.pylintrc` is correct and that the `PYLINT_PATH` in the audit script points to the executable inside your Odoo virtual environment.

#### **Issue 7.2: The Live Installation Test Fails Silently**

  * **Symptom:** The `2_installation_test_log.txt` is empty or shows no clear error, but the module still fails to install from the Odoo UI.
  * **Root Cause:** The `audit.sh` script failed to capture the error traceback from the Odoo server log.
  * **Solution:** Manually run the installation command provided in the log file directly in your terminal, then immediately check the main Odoo server log for the full traceback using `tail -n 200 /var/log/odoo/odoo-server.log`.